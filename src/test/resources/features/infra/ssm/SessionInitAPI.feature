Feature: Initialization of a Step-Up Session
  As a client application
  I want to initialize a step-up authentication session
  So that I can authenticate users with various factors

  Scenario Outline: Successfully initialize step-up authentication session
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    When I create a step-up session using Factor "<factor>" authentication method with Profile Type: "<identifierType>" and config "<authConfigParams>"
    Then The step-up session should be successfully created
    And The session should be configured with Factor "<factor>" authentication method
    And The Factor "<factor>" settings should be correctly configured with "<configValidation>"
    And The session should have status "<sessionStatus>"
    And The active authentication method should be factor "<factor>"

    Examples:
      | factor     | identifierType |sessionStatus |dataType | authConfigParams                                   | configValidation          |
      | OTP        | PROFILE_ID     |IN_PROGRESS   |phone    | /dataShare/successCase/OTP_authConfig.json         | enriched with profile data|
      | DEVICE_BIO | PROFILE_ID     |IN_PROGRESS   |device   | /dataShare/successCase/DEVICE_BIO_authConfig.json  | provided device data      |
      | PASSCODE   | PROFILE_ID     |IN_PROGRESS   |device   | /dataShare/successCase/PASS_CODE_authConfig.json   | device information        |
      | FACIAL     | PROFILE_ID     |IN_PROGRESS   |facial   | /dataShare/successCase/FACIAL_authConfig.json      | facial recognition config |

#
  Scenario Outline: Fail to initialize step-up session due to invalid conditions
    Given <precondition>
    When I create a step-up session using Factor "<factor>" authentication method with Profile Type: "<identifierType>" and config "<authConfigParams>"
    Then The service should return an error with code <errorCode> and have message: <message>
#
    Examples:
      | precondition                                        | factor     | identifierType | authConfigParams                                           | errorCode                            |message                                   |
      | A user profile does not exist in the system         | OTP        | PROFILE_ID     | /dataShare/failCase/OTP_authConfig.json                    | PROFILE_NOT_FOUND                    |Profile not found                         |
      | A profile exists but device is not linked           | DEVICE_BIO | PROFILE_ID     | /dataShare/failCase/DEVICE_BIO_authConfig.json             | INVALID_REQUEST_BODY           |All factors are ineligible                |
      | The request is missing the identifierId             | PASSCODE   | PROFILE_ID     | /dataShare/failCase/PASS_CODE_authConfig.json              | PROFILE_NOT_FOUND                    |Profile not found                         |

#  Scenario Outline: Fail to initialize step-up session due to invalid conditions
#    Given <precondition>
#    When I create a step-up session using Factor "<factor>" authentication method with Profile Type: "<identifierType>" and config "<authConfigParams>"
#    Then The service should return an error with code "<errorCode>"
#    Examples:
#      | precondition | factor | identifierType | authConfigParams | errorCode |