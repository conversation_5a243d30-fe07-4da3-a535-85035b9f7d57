package com.tyme.tymex.stepupauth.cucumber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import com.tyme.tymex.stepupauth.domain.ScenarioContext;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Log4j2
@SpringBootTest
@AutoConfigureMockMvc
public class SessionUpdateStepDefs extends StepUpAuthApplicationTestsBase{

    @Autowired
    ScenarioContext scenarioContext;

    @Autowired
    StepUpRepo stepUpRepo;

    ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    MockMvc mockMvc;

    private ResultActions lastResultActions;
    private String nonExistentSessionId;
    private Exception thrownException;

    // ========================================
    // WHEN STEP DEFINITIONS
    // ========================================

    @When("I send an update request for the session with factor {string}, status {string}, attempt {int}, and maxAttempts {int}")
    public void iSendAnUpdateRequestForTheSessionWithFactorStatusAttemptAndMaxAttempts(String factor, String status, int attempt, int maxAttempts) throws Exception {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        boolean passed = "SUCCESS".equals(status);

        StepUpUpdateRequest updateRequest = StepUpUpdateRequest.builder()
                .stepUpAuthId(stepUpAuthId)
                .authFactor(authFactor)
                .passed(passed)
                .attempts(attempt)
                .maxAttempts(maxAttempts)
                .build();

        log.info("Sending update request: {}", updateRequest);

        try {
            lastResultActions = mockMvc.perform(put("/internal/step-up")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("X-API-VERSION", "1")
                    .content(objectMapper.writeValueAsString(updateRequest)));
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception during update request: {}", e.getMessage());
        }
    }

    @When("I send an update request for the session with factor {string}, status {string}, attempt {int}, maxAttempts {int}, and keepCurrentFactor {}")
    public void iSendAnUpdateRequestWithKeepCurrentFactor(String factor, String status, int attempt, int maxAttempts, boolean keepCurrentFactor) throws Exception {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        boolean passed = "SUCCESS".equals(status);

        StepUpUpdateRequest updateRequest = StepUpUpdateRequest.builder()
                .stepUpAuthId(stepUpAuthId)
                .authFactor(authFactor)
                .passed(passed)
                .attempts(attempt)
                .maxAttempts(maxAttempts)
                .isKeepCurrentFactor(keepCurrentFactor)
                .build();

        log.info("Sending update request with keepCurrentFactor: {}", updateRequest);

        try {
            lastResultActions = mockMvc.perform(put("/internal/step-up")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("X-API-VERSION", "1")
                    .content(objectMapper.writeValueAsString(updateRequest)));
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception during update request: {}", e.getMessage());
        }
    }

    @Then("The service should respond with HTTP status {int}")
    public void theServiceShouldRespondWithHTTPStatus(int expectedStatusCode) throws Exception {
        Assertions.assertNull(thrownException, "No exception should be thrown during update request");
        Assertions.assertNotNull(lastResultActions, "Result actions should not be null");
        lastResultActions.andExpect(status().is(expectedStatusCode));
        log.info("Service responded with HTTP status: {}", expectedStatusCode);
    }

    @And("The overall session status in the database should be {string}")
    public void theOverallSessionStatusInTheDatabaseShouldBe(String expectedStatus) {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(stepUpAuthId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(stepUpAuthId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(StepUpStatus.valueOf(expectedStatus), sessionEntity.getOverallStatus(),
                "Session status should be " + expectedStatus);
        log.info("Session status in database is: {}", expectedStatus);
    }

    @And("The record for factor {string} should be updated with status {string} and attempt count {int}")
    public void theRecordForFactorShouldBeUpdatedWithStatusAndAttemptCount(String factorStr, String expectedStatus, int expectedAttemptCount) {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        AuthFactor factor = AuthFactor.valueOf(factorStr);

        StepUpEntity factorEntity = stepUpRepo.queryByAuthId(stepUpAuthId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toFactorSk(factor)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(factorEntity, "Factor entity should exist in database");
        Assertions.assertEquals(FactorStatus.valueOf(expectedStatus), factorEntity.getFactorResult(),
                "Factor status should be " + expectedStatus);
        Assertions.assertEquals(expectedAttemptCount, factorEntity.getAttempt(),
                "Attempt count should be " + expectedAttemptCount);
        log.info("Factor {} status is {} with attempt count {}", factorStr, expectedStatus, expectedAttemptCount);
    }

    @And("The session should have current factor {string}")
    public void theSessionShouldHaveCurrentFactor(String expectedCurrentFactor) {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(stepUpAuthId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(stepUpAuthId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(AuthFactor.valueOf(expectedCurrentFactor), sessionEntity.getCurrentFactor(),
                "Current factor should be " + expectedCurrentFactor);
        log.info("Session current factor is: {}", expectedCurrentFactor);
    }

    @Given("A step-up session ID does not exist in the database")
    public void aStepUpSessionIDDoesNotExistInTheDatabase() {
        nonExistentSessionId = "NON_EXISTENT_" + UUID.randomUUID();
        log.info("Using non-existent session ID: {}", nonExistentSessionId);
    }

    @When("I send an update request for non-existent session with factor {string}, status {string}, attempt {int}, and maxAttempts {int}")
    public void iSendAnUpdateRequestForNonExistentSession(String factor, String status, int attempt, int maxAttempts) throws Exception {
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        boolean passed = "SUCCESS".equals(status);

        StepUpUpdateRequest updateRequest = StepUpUpdateRequest.builder()
                .stepUpAuthId(nonExistentSessionId)
                .authFactor(authFactor)
                .passed(passed)
                .attempts(attempt)
                .maxAttempts(maxAttempts)
                .build();

        log.info("Sending update request for non-existent session: {}", updateRequest);

        try {
            lastResultActions = mockMvc.perform(put("/internal/step-up")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("X-API-VERSION", "1")
                    .content(objectMapper.writeValueAsString(updateRequest)));
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception during update request for non-existent session: {}", e.getMessage());
        }
    }

    @And("The response should contain error code {string}")
    public void theResponseShouldContainErrorCode(String expectedErrorCode) throws Exception {
        Assertions.assertNotNull(lastResultActions, "Result actions should not be null");
        String responseContent = lastResultActions.andReturn().getResponse().getContentAsString();
        Assertions.assertTrue(responseContent.contains(expectedErrorCode),
                "Response should contain error code: " + expectedErrorCode);
        log.info("Response contains expected error code: {}", expectedErrorCode);
    }

    @When("I send an update request with invalid parameters")
    public void iSendAnUpdateRequestWithInvalidParameters() throws Exception {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();

        // Create request with invalid parameters (negative attempt count)
        StepUpUpdateRequest updateRequest = StepUpUpdateRequest.builder()
                .stepUpAuthId(stepUpAuthId)
                .authFactor(AuthFactor.OTP)
                .passed(true)
                .attempts(-1) // Invalid negative attempt
                .maxAttempts(3)
                .build();

        log.info("Sending update request with invalid parameters: {}", updateRequest);

        try {
            lastResultActions = mockMvc.perform(put("/internal/step-up")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("X-API-VERSION", "1")
                    .content(objectMapper.writeValueAsString(updateRequest)));
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception during invalid update request: {}", e.getMessage());
        }
    }

    @And("The response should contain validation error")
    public void theResponseShouldContainValidationError() throws Exception {
        Assertions.assertNotNull(lastResultActions, "Result actions should not be null");
        String responseContent = lastResultActions.andReturn().getResponse().getContentAsString();
        // Check for validation error indicators
        Assertions.assertTrue(responseContent.contains("validation") || responseContent.contains("invalid") ||
                responseContent.contains("error"), "Response should contain validation error");
        log.info("Response contains validation error");
    }
}

