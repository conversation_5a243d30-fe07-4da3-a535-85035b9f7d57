package com.tyme.tymex.stepupauth.cucumber;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import com.tyme.tymex.stepupauth.domain.ScenarioContext;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import com.tyme.tymex.stepupauth.utils.HttpUtil;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.UUID;

@Log4j2
@SpringBootTest
public class SessionUpdateStepDefs extends StepUpAuthApplicationTestsBase{

    @Autowired
    ScenarioContext scenarioContext;

    @Autowired
    StepUpRepo stepUpRepo;

    @Autowired
    StepUpService stepUpService;

    private String nonExistentSessionId;
    private Exception thrownException;


    @When("I send an update request for the session with factor {string}, status {string}, attempt {int}, and maxAttempts {int}")
    public void iSendAnUpdateRequestForTheSessionWithFactorStatusAttemptAndMaxAttempts(String factor, String status, int attempt, int maxAttempts) {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        boolean passed = "SUCCESS".equals(status);

        StepUpUpdateRequest updateRequest = StepUpUpdateRequest.builder()
                .stepUpAuthId(stepUpAuthId)
                .authFactor(authFactor)
                .passed(passed)
                .attempts(attempt)
                .maxAttempts(maxAttempts)
                .build();

        log.info("Sending update request: {}", updateRequest);

        try {
            stepUpService.updateStepUpSession(updateRequest);
            log.info("Update request completed successfully");
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception during update request: {}", e.getMessage());
        }
    }

    @When("I send an update request for the session with factor {string}, status {string}, attempt {int}, maxAttempts {int}, and keepCurrentFactor {word}")
    public void iSendAnUpdateRequestWithKeepCurrentFactor(String factor, String status, int attempt, int maxAttempts, String keepCurrentFactorStr) {
        boolean keepCurrentFactor = Boolean.parseBoolean(keepCurrentFactorStr);
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        boolean passed = "SUCCESS".equals(status);

        StepUpUpdateRequest updateRequest = StepUpUpdateRequest.builder()
                .stepUpAuthId(stepUpAuthId)
                .authFactor(authFactor)
                .passed(passed)
                .attempts(attempt)
                .maxAttempts(maxAttempts)
                .isKeepCurrentFactor(keepCurrentFactor)
                .build();

        log.info("Sending update request with keepCurrentFactor: {}", updateRequest);

        try {
            stepUpService.updateStepUpSession(updateRequest);
            log.info("Update request with keepCurrentFactor completed successfully");
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception during update request: {}", e.getMessage());
        }
    }

    @Then("The service should respond with HTTP status {int}")
    public void theServiceShouldRespondWithHTTPStatus(int expectedStatusCode) {
        if (expectedStatusCode == 200) {
            Assertions.assertNull(thrownException, "No exception should be thrown for successful update request");
            log.info("Service completed successfully (equivalent to HTTP 200)");
        } else if (expectedStatusCode == 400) {
            Assertions.assertNotNull(thrownException, "Exception should be thrown for validation error (HTTP 400)");
            log.info("Service threw exception as expected (equivalent to HTTP {}): {}", expectedStatusCode, thrownException.getMessage());
        } else {
            Assertions.assertNotNull(thrownException, "Exception should be thrown for error cases");
            log.info("Service threw exception as expected (equivalent to HTTP {}): {}", expectedStatusCode, thrownException.getMessage());
        }
    }

    @And("The overall session status in the database should be {string}")
    public void theOverallSessionStatusInTheDatabaseShouldBe(String expectedStatus) {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(stepUpAuthId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(stepUpAuthId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");

        // Log actual values for debugging
        log.info("Expected session status: {}, Actual session status: {}", expectedStatus, sessionEntity.getOverallStatus());
        log.info("Session entity details: {}", sessionEntity);

        Assertions.assertEquals(StepUpStatus.valueOf(expectedStatus), sessionEntity.getOverallStatus(),
                "Session status should be " + expectedStatus + " but was " + sessionEntity.getOverallStatus());
        log.info("Session status in database is: {}", expectedStatus);
    }

    @And("The record for factor {string} should be updated with status {string} and attempt count {int}")
    public void theRecordForFactorShouldBeUpdatedWithStatusAndAttemptCount(String factorStr, String expectedStatus, int expectedAttemptCount) {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        AuthFactor factor = AuthFactor.valueOf(factorStr);

        StepUpEntity factorEntity = stepUpRepo.queryByAuthId(stepUpAuthId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toFactorSk(factor)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(factorEntity, "Factor entity should exist in database");
        Assertions.assertEquals(FactorStatus.valueOf(expectedStatus), factorEntity.getFactorResult(),
                "Factor status should be " + expectedStatus);
        Assertions.assertEquals(expectedAttemptCount, factorEntity.getAttempt(),
                "Attempt count should be " + expectedAttemptCount);
        log.info("Factor {} status is {} with attempt count {}", factorStr, expectedStatus, expectedAttemptCount);
    }

    @And("The session should have current factor {string}")
    public void theSessionShouldHaveCurrentFactor(String expectedCurrentFactor) {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(stepUpAuthId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(stepUpAuthId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");

        // Log actual values for debugging
        log.info("Expected current factor: {}, Actual current factor: {}", expectedCurrentFactor, sessionEntity.getCurrentFactor());
        log.info("Session entity details: {}", sessionEntity);

        if ("null".equals(expectedCurrentFactor)) {
            Assertions.assertNull(sessionEntity.getCurrentFactor(),
                    "Current factor should be null but was " + sessionEntity.getCurrentFactor());
        } else {
            Assertions.assertEquals(AuthFactor.valueOf(expectedCurrentFactor), sessionEntity.getCurrentFactor(),
                    "Current factor should be " + expectedCurrentFactor + " but was " + sessionEntity.getCurrentFactor());
        }
        log.info("Session current factor is: {}", expectedCurrentFactor);
    }

    @Given("A step-up session ID does not exist in the database")
    public void aStepUpSessionIDDoesNotExistInTheDatabase() {
        nonExistentSessionId = "NON_EXISTENT_" + UUID.randomUUID();
        log.info("Using non-existent session ID: {}", nonExistentSessionId);
    }

    @When("I send an update request for non-existent session with factor {string}, status {string}, attempt {int}, and maxAttempts {int}")
    public void iSendAnUpdateRequestForNonExistentSession(String factor, String status, int attempt, int maxAttempts) {
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        boolean passed = "SUCCESS".equals(status);

        StepUpUpdateRequest updateRequest = StepUpUpdateRequest.builder()
                .stepUpAuthId(nonExistentSessionId)
                .authFactor(authFactor)
                .passed(passed)
                .attempts(attempt)
                .maxAttempts(maxAttempts)
                .build();

        log.info("Sending update request for non-existent session: {}", updateRequest);

        try {
            stepUpService.updateStepUpSession(updateRequest);
            log.info("Update request for non-existent session completed unexpectedly");
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception during update request for non-existent session: {}", e.getMessage());
        }
    }

    @And("The response should contain error code {string}")
    public void theResponseShouldContainErrorCode(String expectedErrorCode) {
        Assertions.assertNotNull(thrownException, "Exception should be thrown");
        Assertions.assertTrue(thrownException instanceof DomainException, "Exception should be DomainException");

        DomainException domainException = (DomainException) thrownException;
        String actualErrorCode = domainException.getErrorCode().name();
        Assertions.assertEquals(expectedErrorCode, actualErrorCode,
                "Error code should be: " + expectedErrorCode);
        log.info("Exception contains expected error code: {}", expectedErrorCode);
    }

    @When("I send an update request with invalid parameters")
    public void iSendAnUpdateRequestWithInvalidParameters() {
        String stepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();

        // Create request with invalid parameters (null authFactor)
        StepUpUpdateRequest updateRequest = StepUpUpdateRequest.builder()
                .stepUpAuthId(stepUpAuthId)
                .authFactor(null) // Invalid null factor
                .passed(true)
                .attempts(1)
                .maxAttempts(3)
                .build();

        log.info("Sending update request with invalid parameters: {}", updateRequest);

        try {
            stepUpService.updateStepUpSession(updateRequest);
            log.info("Update request with invalid parameters completed unexpectedly");
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception during invalid update request: {}", e.getMessage());
        }
    }

    @And("The response should contain validation error")
    public void theResponseShouldContainValidationError() {
        Assertions.assertNotNull(thrownException, "Exception should be thrown for validation error");
        // Check for validation error indicators in exception message
        String errorMessage = thrownException.getMessage().toLowerCase();
        Assertions.assertTrue(errorMessage.contains("validation") || errorMessage.contains("invalid") ||
                errorMessage.contains("constraint"), "Exception should contain validation error message");
        log.info("Exception contains validation error: {}", thrownException.getMessage());
    }
}

